```mermaid
flowchart TD
    A[任务合并核心逻辑] --> B[按类型分组处理]
    
    B --> C[店铺任务合并 mergeMerchantTasks]
    B --> D[受付任务合并 mergeReceptionTasks]
    
    C --> E[按以下余白分组 groupTaskByEndFlag]
    D --> F[直接处理受付任务]
    
    E --> G{检查分组}
    G -->|有未识别任务| H[跳过本组]
    G -->|全QR但未上传| I[记录跳过任务]
    G -->|可处理| J[调用 mergeTaskList]
    
    F --> K[调用 mergeReceptionTaskList]
    
    J --> L[分析标准姓名和生日]
    K --> M[分析标准汉字和假名]
    
    L --> N[遍历任务进行匹配]
    M --> O[遍历任务进行匹配]
    
    N --> P{姓名或生日匹配?}
    O --> Q{汉字或假名匹配?}
    
    P -->|否| R[跳过此任务]
    P -->|是| S[检查任务类型组合]
    
    Q -->|否| T[跳过此任务]
    Q -->|是| U[检查任务类型组合]
    
    S --> V{前一个任务类型}
    U --> W{前一个任务类型}
    
    V -->|第一个任务| X[初始化基准任务]
    V -->|QR任务| Y[QR任务处理]
    V -->|OCR任务| Z[OCR任务处理]
    
    W -->|第一个任务| AA[初始化基准任务]
    W -->|QR任务| BB[QR任务处理]
    W -->|OCR任务| CC[OCR任务处理]
    
    Y --> DD{当前任务类型}
    Z --> EE{当前任务类型}
    BB --> FF{当前任务类型}
    CC --> GG{当前任务类型}
    
    DD -->|QR| HH[QR-QR不合并]
    DD -->|OCR| II[QR-OCR检查医生匹配]
    
    EE -->|QR| JJ[OCR-QR检查医生科室匹配]
    EE -->|OCR| KK[OCR-OCR检查医生科室匹配]
    
    FF -->|QR| LL[QR-QR不合并]
    FF -->|OCR| MM[QR-OCR检查医生匹配]
    
    GG -->|QR| NN[OCR-QR检查医生科室匹配]
    GG -->|OCR| OO[OCR-OCR检查医生科室匹配]
    
    II --> PP{医生匹配?}
    JJ --> QQ{医生科室匹配?}
    KK --> RR{医生科室匹配?}
    MM --> SS{医生匹配?}
    NN --> TT{医生科室匹配?}
    OO --> UU{医生科室匹配?}
    
    PP -->|是| VV[记录合并的taskId]
    PP -->|否| WW[拒绝合并]
    
    QQ -->|是| XX[记录合并的taskId]
    QQ -->|否| YY[拒绝合并]
    
    RR -->|是| ZZ[记录合并的taskId]
    RR -->|否| AAA[拒绝合并]
    
    SS -->|是| BBB[记录合并的taskId]
    SS -->|否| CCC[拒绝合并]
    
    TT -->|是| DDD[记录合并的taskId]
    TT -->|否| EEE[拒绝合并]
    
    UU -->|是| FFF[记录合并的taskId]
    UU -->|否| GGG[拒绝合并]
    
    VV --> HHH{是否有合并}
    XX --> HHH
    ZZ --> HHH
    BBB --> HHH
    DDD --> HHH
    FFF --> HHH
    
    WW --> HHH
    YY --> HHH
    AAA --> HHH
    CCC --> HHH
    EEE --> HHH
    GGG --> HHH
    
    HHH -->|有合并| III[递归调用合并]
    HHH -->|无合并| JJJ[调用combineTaskData批量合并taskIds]
    
    III --> J
    III --> K
    
    JJJ --> KKK[调用 endMergedTask]
    
    KKK --> LLL{检查任务状态}
    LLL -->|需要延迟| MMM[跳过结束]
    LLL -->|QR任务| NNN[收集QR任务]
    LLL -->|OCR尾页或超时| OOO[调用 finishPrescription]
    
    NNN --> PPP[批量处理QR任务]
    PPP --> QQQ[更新Redis状态]
    PPP --> RRR[处理被合并任务]
    
    OOO --> SSS[保存到Original表]
    OOO --> TTT[生成QR数据]
    OOO --> UUU[添加通知]
    OOO --> VVV[上传图片]
    
    H --> KKK
    I --> KKK
    R --> N
    T --> O
    X --> N
    AA --> O
    HH --> N
    LL --> O
    MMM --> WWW[完成]
    
    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef business fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef success fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef error fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef database fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20

    %% 应用样式类
    class A startEnd
    class B,C,D,E,F,J,K,L,M,N,O process
    class G,P,Q,V,W,DD,EE,FF,GG,PP,QQ,RR,SS,TT,UU,HHH,LLL decision
    class H,I,R,T,WW,YY,AAA,CCC,EEE,GGG error
    class X,AA,Y,BB,Z,CC process
    class VV,XX,ZZ,BBB,DDD,FFF success
    class III business
    class JJJ,KKK,MMM,NNN,OOO,PPP,QQQ,RRR,SSS,TTT,UUU,VVV,WWW database
```
