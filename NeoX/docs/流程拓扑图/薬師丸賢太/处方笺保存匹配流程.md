```mermaid
flowchart TD
    subgraph "NSIPS 匹配服务完整流程总结"
        
        subgraph "输入数据"
            INPUT[处方信息 PrescriptionSearchInfo<br/>✓ NameKanji 患者姓名<br/>✓ Birthday 生日<br/>✓ PeBeneficiaryNo 公费受给番号<br/>✓ PeBearerNo 被保险者记号<br/>✓ HospitalCode 医院编码<br/>✓ DepartName 科室名称<br/>✓ DocName 医生姓名<br/>✓ DeliveryDate 交付日期]
        end
        
        subgraph "匹配流程"
            STEP1[1️⃣ 初步筛选<br/>返回所有NSIPS记录]
            
            STEP2[2️⃣ PeBeneficiaryNo 优先匹配<br/>🔥 最高优先级<br/>精确匹配，找到即返回]
            
            STEP3[3️⃣ 按顺序逐步过滤<br/>每步检查候选数量<br/>唯一匹配或无候选时立即返回]
            
            subgraph "过滤顺序与策略"
                F1[PeBearerNo<br/>宽松匹配]
                F2[NameKanji<br/>精确匹配]
                F3[DocName<br/>宽松匹配]
                F4[HospitalCode<br/>精确匹配]
                F5[DeliveryDate<br/>灵活匹配]
                F6[Birthday<br/>灵活匹配]
                F7[DepartName<br/>模糊匹配]
                
                F1 --> F2 --> F3 --> F4 --> F5 --> F6 --> F7
            end
        end
        
        subgraph "匹配策略说明"
            STRATEGY1[精确匹配<br/>完全相等比较<br/>移除空格后匹配]
            STRATEGY2[宽松匹配<br/>NSIPS无值时跳过<br/>有值时必须匹配]
            STRATEGY3[灵活匹配<br/>支持字符串和数字<br/>多种格式兼容]
            STRATEGY4[模糊匹配<br/>去除格式差异<br/>包含关系比较]
        end
        
        subgraph "输出结果"
            RESULT1[✅ 唯一匹配<br/>返回单一结果]
            RESULT2[⚠️ 多个候选<br/>返回候选列表]
            RESULT3[❌ 无匹配<br/>返回空结果]
        end
        
        subgraph "核心特性"
            FEATURE1[🎯 早期返回机制<br/>每步过滤后立即检查<br/>避免不必要的计算]
            
            FEATURE2[🛡️ 健壮性保证<br/>异常处理机制<br/>空值安全检查]
            
            FEATURE3[📊 详细日志输出<br/>每步过滤记录<br/>匹配结果对比]
            
            FEATURE4[⚡ 性能优化<br/>优先级排序<br/>渐进式过滤]
        end
        
        INPUT --> STEP1
        STEP1 --> STEP2
        STEP2 --> STEP3
        STEP3 --> F1
        
        F1 -.->|候选=1| RESULT1
        F2 -.->|候选=1| RESULT1
        F3 -.->|候选=1| RESULT1
        F4 -.->|候选=1| RESULT1
        F5 -.->|候选=1| RESULT1
        F6 -.->|候选=1| RESULT1
        
        F1 -.->|候选=0| RESULT3
        F2 -.->|候选=0| RESULT3
        F3 -.->|候选=0| RESULT3
        F4 -.->|候选=0| RESULT3
        F5 -.->|候选=0| RESULT3
        F6 -.->|候选=0| RESULT3
        
        F7 --> EVAL[最终评估]
        EVAL --> RESULT1
        EVAL --> RESULT2
        EVAL --> RESULT3
        
        STEP2 -.->|找到匹配| RESULT1
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef database fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef cache fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef output fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef error fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c

    %% 应用样式类
    class INPUT startEnd
    class STEP1,STEP2,STEP3,F1,F2,F3,F4,F5,F6,F7,EVAL process
    class STRATEGY1,STRATEGY2,STRATEGY3,STRATEGY4 decision
    class RESULT1 output
    class RESULT2 cache
    class RESULT3 error
    class FEATURE1,FEATURE2,FEATURE3,FEATURE4 database
```
